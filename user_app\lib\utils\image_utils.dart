import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import '../constants/app_constants.dart';
import '../config/image_cache_config.dart';

class ImageUtils {
  // Build optimized product image with caching
  static Widget buildProductImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    String imageType = 'product_card',
  }) {
    final settings = ImageCacheConfig.getOptimalSettings(imageType);
    
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      cacheManager: ImageCacheConfig.productImageCacheManager,
      memCacheWidth: settings['memCacheWidth'],
      memCacheHeight: settings['memCacheHeight'],
      maxWidthDiskCache: settings['maxWidthDiskCache'],
      maxHeightDiskCache: settings['maxHeightDiskCache'],
      placeholder: (context, url) => _buildPlaceholder(width, height),
      errorWidget: (context, url, error) => _buildErrorWidget(width, height),
      fadeInDuration: const Duration(milliseconds: 200),
      fadeOutDuration: const Duration(milliseconds: 100),
    );
  }

  // Build optimized profile image
  static Widget buildProfileImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
  }) {
    final settings = ImageCacheConfig.getOptimalSettings('profile_image');
    
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      cacheManager: ImageCacheConfig.profileImageCacheManager,
      memCacheWidth: settings['memCacheWidth'],
      memCacheHeight: settings['memCacheHeight'],
      maxWidthDiskCache: settings['maxWidthDiskCache'],
      maxHeightDiskCache: settings['maxHeightDiskCache'],
      placeholder: (context, url) => _buildProfilePlaceholder(width, height),
      errorWidget: (context, url, error) => _buildProfileErrorWidget(width, height),
      fadeInDuration: const Duration(milliseconds: 150),
    );
  }

  // Build optimized post image
  static Widget buildPostImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
  }) {
    final settings = ImageCacheConfig.getOptimalSettings('post_image');
    
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      cacheManager: ImageCacheConfig.postImageCacheManager,
      memCacheWidth: settings['memCacheWidth'],
      memCacheHeight: settings['memCacheHeight'],
      maxWidthDiskCache: settings['maxWidthDiskCache'],
      maxHeightDiskCache: settings['maxHeightDiskCache'],
      placeholder: (context, url) => _buildPlaceholder(width, height),
      errorWidget: (context, url, error) => _buildErrorWidget(width, height),
      fadeInDuration: const Duration(milliseconds: 200),
    );
  }

  // Build product preview image (smaller, faster loading)
  static Widget buildProductPreview({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
  }) {
    final settings = ImageCacheConfig.getOptimalSettings('product_preview');
    
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      cacheManager: ImageCacheConfig.productImageCacheManager,
      memCacheWidth: settings['memCacheWidth'],
      memCacheHeight: settings['memCacheHeight'],
      maxWidthDiskCache: settings['maxWidthDiskCache'],
      maxHeightDiskCache: settings['maxHeightDiskCache'],
      placeholder: (context, url) => _buildSmallPlaceholder(width, height),
      errorWidget: (context, url, error) => _buildSmallErrorWidget(width, height),
      fadeInDuration: const Duration(milliseconds: 100),
    );
  }

  // Generic placeholder
  static Widget _buildPlaceholder(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.backgroundColor,
            AppConstants.backgroundColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: const Center(
        child: CircularProgressIndicator(
          color: AppConstants.primaryColor,
          strokeWidth: 2,
        ),
      ),
    );
  }

  // Generic error widget
  static Widget _buildErrorWidget(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.backgroundColor,
            AppConstants.backgroundColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: const Center(
        child: Icon(
          Icons.image_outlined,
          size: AppConstants.iconSizeLarge,
          color: AppConstants.textHintColor,
        ),
      ),
    );
  }

  // Profile placeholder
  static Widget _buildProfilePlaceholder(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.primaryColor.withOpacity(0.3),
            AppConstants.secondaryColor.withOpacity(0.3),
          ],
        ),
      ),
      child: const Center(
        child: CircularProgressIndicator(
          color: AppConstants.primaryColor,
          strokeWidth: 2,
        ),
      ),
    );
  }

  // Profile error widget
  static Widget _buildProfileErrorWidget(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.primaryColor.withOpacity(0.2),
            AppConstants.secondaryColor.withOpacity(0.2),
          ],
        ),
      ),
      child: const Center(
        child: Icon(
          Icons.person,
          size: AppConstants.iconSizeLarge,
          color: AppConstants.textHintColor,
        ),
      ),
    );
  }

  // Small placeholder for previews
  static Widget _buildSmallPlaceholder(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      color: AppConstants.backgroundColor,
      child: const Center(
        child: SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            color: AppConstants.primaryColor,
            strokeWidth: 1.5,
          ),
        ),
      ),
    );
  }

  // Small error widget for previews
  static Widget _buildSmallErrorWidget(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      color: AppConstants.backgroundColor,
      child: const Center(
        child: Icon(
          Icons.broken_image,
          size: 20,
          color: AppConstants.textHintColor,
        ),
      ),
    );
  }

  // Preload images for better performance
  static Future<void> preloadProductImages(List<String> imageUrls) async {
    for (String url in imageUrls) {
      await ImageCacheConfig.preloadImage(url, 'product');
    }
  }

  // Clear cache when needed
  static Future<void> clearImageCache(String cacheType) async {
    await ImageCacheConfig.clearCache(cacheType);
  }

  // Validate image URL
  static bool isValidImageUrl(String url) {
    if (url.isEmpty) return false;
    
    final uri = Uri.tryParse(url);
    if (uri == null) return false;
    
    final validExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    final path = uri.path.toLowerCase();
    
    return validExtensions.any((ext) => path.endsWith('.$ext')) ||
           url.contains('cloudinary.com') || // Cloudinary URLs
           url.contains('firebasestorage.googleapis.com'); // Firebase Storage URLs
  }
}
